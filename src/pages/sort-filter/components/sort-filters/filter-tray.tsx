import type {
  FilterConfig,
  FilterItem,
  FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import { FilterType } from "@/clients/gen/broking/FilterSearch_pb";
import Checkbox from "@/components/ui/checkbox/checkbox";
import Switch from "@/components/ui/switch/switch";
import Chip from "@/components/ui/chip/chip";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import LinkButton from "@/components/ui/button/link-button";
import Button from "@/components/ui/button/button";
import type { AppliedFilter, FilterOptionValueWithLabel } from "../../machine";

interface FilterTrayProps {
  filterConfig?: FilterConfig;
  totalResults: number;
  toggleFilterPanel: () => void;
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
  handleClearFilters: () => void;
  isLoading: boolean;
}

interface FilterItemRendererProps {
  item: FilterItem;
  handleFilter: (
    filters: FilterOptionValue,
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
}

const FilterItemRenderer = ({
  item,
  handleFilter,
  appliedFilters,
}: FilterItemRendererProps) => {
  const renderFilterContent = () => {
    switch (item.type) {
      case FilterType.MULTI_SELECT_PILLS:
        return (
          <div className="mt-3 flex flex-wrap gap-2">
            {item.options.map((option, index) => (
              <Chip
                key={`${item.key}-${index}`}
                selected={appliedFilters.some(
                  (filter) =>
                    filter.key === item.key &&
                    filter.filters.includes(
                      option.optionValue as FilterOptionValueWithLabel
                    )
                )}
                size="medium"
                variant="outlined"
                onClick={() => {
                  handleFilter(option.optionValue!, !option.isSelected, index);
                }}
              >
                {option.label}
              </Chip>
            ))}
          </div>
        );
      case FilterType.RANGE_MULTI_SELECT:
      case FilterType.MULTI_SELECT:
        return (
          <div className="mt-3 space-y-3">
            {item.options.map((option, index) => (
              <Checkbox
                key={`${item.key}-${index}`}
                name={`${item.key}-${index}`}
                defaultChecked={appliedFilters.some(
                  (filter) =>
                    filter.key === item.key &&
                    filter.filters.includes(
                      option.optionValue as FilterOptionValueWithLabel
                    )
                )}
                className="text-black-80 flex-row-reverse justify-between"
                onChange={(event) => {
                  handleFilter(
                    option.optionValue!,
                    (event.target as HTMLInputElement).checked,
                    index
                  );
                }}
              >
                <p className="text-body1">{option.label}</p>
              </Checkbox>
            ))}
          </div>
        );

      case FilterType.MULTI_SELECT_WITH_ICON:
        return (
          <div className="mt-3 flex flex-wrap gap-2.5 space-y-2">
            {item.options.map((option, index) => (
              <div
                key={`${item.key}-${index}`}
                className="text-body1 flex items-center gap-2"
              >
                <Chip
                  selected={appliedFilters.some(
                    (filter) =>
                      filter.key === item.key &&
                      filter.filters.includes(
                        option.optionValue as FilterOptionValueWithLabel
                      )
                  )}
                  onClick={(isSelected) => {
                    handleFilter(
                      option.optionValue!,
                      isSelected as boolean,
                      index
                    );
                  }}
                >
                  <img src={option.icon} className="h-4 w-4 object-contain" />
                  {option.label}
                </Chip>
              </div>
            ))}
          </div>
        );

      case FilterType.BOOLEAN_SELECT:
        return null;

      default:
        return (
          <div className="mt-3 space-y-3">
            {item.options.map((option, index) => (
              <Checkbox
                key={`${item.key}-${index}`}
                name={`${item.key}-${index}`}
                defaultChecked={option.isSelected}
                className="text-body1 flex-row-reverse justify-between"
                onChange={(event) => {
                  handleFilter(
                    option.optionValue!,
                    (event.target as HTMLInputElement).checked,
                    index
                  );
                }}
              >
                {option.label}
              </Checkbox>
            ))}
          </div>
        );
    }
  };

  if (item.type === FilterType.BOOLEAN_SELECT) {
    return (
      <div className="py-4">
        <div className="flex items-center justify-between">
          <span className="text-body1 text-black-80">{item.label}</span>
          <Switch
            name={item.key}
            defaultChecked={item.options[0]?.isSelected || false}
            onChange={(event) => {
              if (item.options[0]?.optionValue) {
                handleFilter(
                  item.options[0].optionValue,
                  (event.target as HTMLInputElement).checked,
                  0
                );
              }
            }}
          />
        </div>
      </div>
    );
  }

  if (item.isCollapsible) {
    return (
      <div className="">
        <Accordion
          defaultValue={item.isCollapsed ? [] : [item.key]}
          className="m-0! p-0!"
        >
          <AccordionItem
            id={item.key}
            label={
              <h4 className="text-heading4 text-black-80">{item.label}</h4>
            }
          >
            {renderFilterContent()}
          </AccordionItem>
        </Accordion>
      </div>
    );
  }

  return (
    <div className="py-4">
      <div className="space-y-2">
        <h4 className="text-heading4 text-black-80">{item.label}</h4>
        {renderFilterContent()}
      </div>
    </div>
  );
};

const FilterTray = ({
  filterConfig,
  totalResults,
  toggleFilterPanel,
  handleFilterChange,
  appliedFilters,
  handleClearFilters,
  isLoading,
}: FilterTrayProps) => {
  return (
    <>
      <div className="flex flex-col px-5 pb-25">
        <h2 className="text-heading3 text-black-80 mb-4 font-medium">
          {filterConfig?.title}
        </h2>

        {filterConfig?.items.map((item, index) => (
          <>
            <hr />
            <FilterItemRenderer
              key={`${item.key}-${index}`}
              item={item}
              handleFilter={(filters, isSelected, selectedIndex) =>
                handleFilterChange(
                  {
                    key: item.key,
                    filters,
                    label: item.options[selectedIndex].label,
                  },
                  isSelected,
                  selectedIndex
                )
              }
              appliedFilters={appliedFilters}
            />
          </>
        ))}
      </div>
      <div className="bg-bg fixed right-0 bottom-0 left-0 flex items-center justify-between p-5">
        {appliedFilters.length > 0 ? (
          <LinkButton className="shrink-0" onClick={handleClearFilters}>
            Clear all
          </LinkButton>
        ) : (
          <p>No filters selected</p>
        )}

        <Button
          className="w-fit! shrink-1"
          onClick={toggleFilterPanel}
          loading={isLoading}
        >
          View {totalResults} results
        </Button>
      </div>
    </>
  );
};

export default FilterTray;
