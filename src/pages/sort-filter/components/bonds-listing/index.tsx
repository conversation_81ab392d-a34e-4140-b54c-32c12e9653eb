import { useEffect, useRef, useCallback } from "react";
import type {
  BondItem,
  FilterItem,
} from "@/clients/gen/broking/FilterSearch_pb";
import BondsCard from "./card";
import BondsCardShimmer from "./shimmer";
import Anchor from "@/components/functional/anchor";
import { trackEvent } from "@/utils/analytics";

interface BondsListingProps {
  listData?: BondItem[];
  onPageChange?: (page: number) => void;
  currentPage?: number;
  totalPages?: number;
  isLoading?: boolean;
  isFilterLoading?: boolean;
  suggestedFilters?: { [key: number]: FilterItem };
}

// Shimmer animation styles
const shimmerStyles = `
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }
`;

// Add shimmer styles to document head if not already present
if (
  typeof document !== "undefined" &&
  !document.getElementById("bonds-shimmer-styles")
) {
  const style = document.createElement("style");
  style.id = "bonds-shimmer-styles";
  style.textContent = shimmerStyles;
  document.head.appendChild(style);
}

const BondsLisiting = ({
  listData,
  onPageChange,
  currentPage = 1,
  totalPages = 1,
  isLoading = false,
  isFilterLoading = false,
  suggestedFilters,
}: BondsListingProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasMorePages = currentPage < totalPages;

  const handleScroll = useCallback(() => {
    if (!containerRef.current || !onPageChange || !hasMorePages || isLoading) {
      return;
    }

    if (throttleTimeoutRef.current) {
      clearTimeout(throttleTimeoutRef.current);
    }

    throttleTimeoutRef.current = setTimeout(() => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;

      if (scrollTop + clientHeight >= scrollHeight - 100) {
        const nextPage = currentPage + 1;
        onPageChange(nextPage);
      }
    }, 100);
  }, [onPageChange, currentPage, hasMorePages, isLoading]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);

      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [handleScroll]);

  if (isFilterLoading) {
    return (
      <div
        className="space-y-4 overflow-y-auto px-5 pt-0 pb-5"
        style={{
          maxHeight: "calc(100vh - 220px)",
        }}
      >
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex flex-col space-y-4">
            <BondsCardShimmer />
            <hr className="ml-13 border-t-[0.5px]" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      <div
        ref={containerRef}
        className="space-y-4 overflow-y-auto bg-white px-5 pt-0 pb-5"
        style={{
          maxHeight: "calc(100vh - 220px)",
        }}
      >
        {listData?.map((item, index) => {
          return (
            <div key={index} className="count flex flex-col space-y-4">
              <Anchor
                href={`/bonds/${item.slug ?? "unknown"}/${item.isin}`}
                onClick={() => {
                  trackEvent("bonds_search_card_clicked", {
                    bond_isin: item.isin,
                    bond_name: item.displayTitle,
                    bond_type: item.investabilityStatus,
                    bond_xirr: item.ytm,
                  });
                }}
                className="space-x-1"
              >
                <BondsCard item={item} />
              </Anchor>
              <hr className="ml-13 border-t-[0.5px]" />
            </div>
          );
        })}
        {isLoading && !isFilterLoading && listData?.length && (
          <>
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={`shimmer-${index}`} className="flex flex-col space-y-4">
                <BondsCardShimmer />
                <hr className="ml-13 border-t-[0.5px]" />
              </div>
            ))}
          </>
        )}
      </div>
    </>
  );
};

export default BondsLisiting;
