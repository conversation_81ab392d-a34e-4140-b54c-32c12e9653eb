import React, { useState, useRef, useEffect } from "react";
import "./select.css";
import ReactDOM from "react-dom";
import clsx from "clsx";
import Checkbox from "@/components/ui/checkbox/checkbox";
import type { AppliedFilter } from "../../machine";

interface Option {
  label: string;
  value: string | number;
}

interface CustomSelectProps {
  options: Option[];
  placeholder?: string;
  onChange: (index: number, isSelected: boolean) => void;
  value?: string | number;
  appliedFilters: AppliedFilter[];
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  options,
  placeholder = "Select",
  onChange,
  appliedFilters,
}) => {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Node;

      const clickedOutside =
        ref.current &&
        !ref.current.contains(target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(target);

      if (clickedOutside) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (open && ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setDropdownStyle({
        position: "absolute",
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width + 40,
        minWidth: 154,
        zIndex: 1000,
      });
    }
  }, [open]);

  const handleSelect = (index: number, isSelected: boolean) => {
    onChange(index, isSelected);
  };

  const checkSelected = (value: string) => {
    return appliedFilters.some((group) =>
      group.filters.some((filter) => filter.label === value)
    );
  };

  return (
    <div className="custom-select" ref={ref}>
      <div
        className={`select-box border-black-10 gap-1 border ${open ? "open" : ""}`}
        onClick={() => setOpen(!open)}
      >
        <span
          className={clsx(
            "text-body1",
            open ? "text-white" : "text-black-80 opacity-70"
          )}
        >
          {placeholder}
        </span>
        <span className="arrow w-[7px]">
          <svg
            width="9"
            height="5"
            viewBox="0 0 9 5"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={clsx(
              "block flex-shrink-0 transition-transform duration-300",
              open ? "rotate-180" : "opacity-80"
            )}
          >
            <path
              d="M8 0.75L4.5 4.25L1 0.75"
              stroke={open ? "white" : "black"}
              stroke-opacity="0.8"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </div>
      {open &&
        ReactDOM.createPortal(
          <ul
            className="select-dropdown space-y-[10px]"
            style={dropdownStyle}
            ref={dropdownRef}
          >
            {options.map((opt, index) => (
              <Checkbox
                key={opt.value}
                onChange={(e) => {
                  e.stopPropagation();
                  handleSelect(index, (e.target as HTMLInputElement).checked);
                }}
                checked={checkSelected(opt.label)}
                className="text-black-80"
              >
                {opt.label}
              </Checkbox>
            ))}
          </ul>,
          document.body
        )}
    </div>
  );
};

export default CustomSelect;
